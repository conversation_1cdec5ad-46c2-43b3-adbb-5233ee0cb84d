generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Admin {
  id       String @id
  username String @unique
}

model Student {
  id          String       @id
  username    String       @unique
  name        String
  surname     String
  email       String?      @unique
  phone       String?      @unique
  address     String
  img         String?
  bloodType   String
  sex         UserSex
  createdAt   DateTime     @default(now())
  parentId    String
  parent      Parent       @relation(fields: [parentId], references: [id])
  classId     Int
  class       Class        @relation(fields: [classId], references: [id])
  gradeId     Int
  grade       Grade        @relation(fields: [gradeId], references: [id])
  attendances Attendance[]
  results     Result[]
  birthday    DateTime
}

model Teacher {
  id        String    @id
  username  String    @unique
  name      String
  surname   String
  email     String?   @unique
  phone     String?   @unique
  address   String
  img       String?
  bloodType String
  sex       UserSex
  createdAt DateTime  @default(now())
  subjects  Subject[]
  lessons   Lesson[]
  classes   Class[]
  birthday  DateTime
}

model Parent {
  id        String    @id
  username  String    @unique
  name      String
  surname   String
  email     String?   @unique
  phone     String    @unique
  address   String
  createdAt DateTime  @default(now())
  students  Student[]
}

model Grade {
  id    Int @id @default(autoincrement())
  level Int @unique

  students Student[]
  classess Class[]
}

model Class {
  id       Int    @id @default(autoincrement())
  name     String @unique
  capacity Int

  supervisorId  String?
  supervisor    Teacher?       @relation(fields: [supervisorId], references: [id])
  lessons       Lesson[]
  students      Student[]
  gradeId       Int
  grade         Grade          @relation(fields: [gradeId], references: [id])
  events        Event[]
  announcements Announcement[]
}

model Subject {
  id       Int       @id @default(autoincrement())
  name     String    @unique
  teachers Teacher[]
  lessons  Lesson[]
}

model Lesson {
  id        Int      @id @default(autoincrement())
  name      String
  day       Day
  startTime DateTime
  endTime   DateTime

  subjectId   Int
  subject     Subject      @relation(fields: [subjectId], references: [id])
  classId     Int
  class       Class        @relation(fields: [classId], references: [id])
  teacherId   String
  teacher     Teacher      @relation(fields: [teacherId], references: [id])
  exams       Exam[]
  assignments Assignment[]
  attendances Attendance[]
}

model Exam {
  id        Int      @id @default(autoincrement())
  title     String
  startTime DateTime
  endTime   DateTime

  lessonId Int
  lesson   Lesson   @relation(fields: [lessonId], references: [id])
  results  Result[]
}

model Assignment {
  id        Int      @id @default(autoincrement())
  title     String
  startDate DateTime
  dueDate   DateTime

  lessonId Int
  lesson   Lesson   @relation(fields: [lessonId], references: [id])
  results  Result[]
}

model Result {
  id    Int @id @default(autoincrement())
  score Int

  examId       Int?
  exam         Exam?       @relation(fields: [examId], references: [id])
  assignmentId Int?
  assignment   Assignment? @relation(fields: [assignmentId], references: [id])
  studentId    String
  student      Student     @relation(fields: [studentId], references: [id])
}

model Attendance {
  id      Int      @id @default(autoincrement())
  date    DateTime
  present Boolean

  studentId String
  student   Student @relation(fields: [studentId], references: [id])
  lessonId  Int
  lesson    Lesson  @relation(fields: [lessonId], references: [id])
}

model Event {
  id          Int      @id @default(autoincrement())
  title       String
  description String
  startTime   DateTime
  endTime     DateTime

  classId Int?
  class   Class? @relation(fields: [classId], references: [id])
}

model Announcement {
  id          Int      @id @default(autoincrement())
  title       String
  description String
  date        DateTime

  classId Int?
  class   Class? @relation(fields: [classId], references: [id])
}

enum UserSex {
  MALE
  FEMALE
}

enum Day {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
}
