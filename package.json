{"name": "school erp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/elements": "^0.23.48", "@clerk/nextjs": "^6.28.1", "@hookform/resolvers": "^5.2.1", "@neondatabase/serverless": "^1.0.1", "@prisma/adapter-neon": "^6.13.0", "@prisma/client": "^6.13.0", "@types/react-big-calendar": "^1.16.2", "moment": "^2.30.1", "next": "15.4.5", "next-cloudinary": "^6.16.0", "prisma": "^6.13.0", "react": "^19.1.1", "react-big-calendar": "^1.19.4", "react-calendar": "^6.0.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-toastify": "^11.0.5", "recharts": "^3.1.0", "ws": "^8.18.3", "zod": "^4.0.14"}, "devDependencies": {"@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/ws": "^8.18.1", "eslint": "^9.32.0", "eslint-config-next": "15.4.5", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "typescript": "^5.9.2"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}